<template>
  <div :class="[
    'mx-auto w-full', 
    padded ? 'px-4 md:px-6 lg:px-8' : '',
    maxWidth === 'sm' ? 'max-w-screen-sm' : '',
    maxWidth === 'md' ? 'max-w-screen-md' : '',
    maxWidth === 'lg' ? 'max-w-screen-lg' : '',
    maxWidth === 'xl' ? 'max-w-screen-xl' : '',
    maxWidth === '2xl' ? '' : '',
    maxWidth === 'full' ? 'max-w-full' : '',
    className
  ]">
    <slot />
  </div>
</template>

<script setup>
defineProps({
  maxWidth: {
    type: String,
    default: 'xl',
    validator: (value) => ['sm', 'md', 'lg', 'xl', '2xl', 'full'].includes(value)
  },
  padded: {
    type: Boolean,
    default: true
  },
  className: {
    type: String,
    default: ''
  }
});
</script> 