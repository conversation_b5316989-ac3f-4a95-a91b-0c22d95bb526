<template>
  <div class="home-page min-h-screen bg-[#0a0d12] relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 bg-gradient-to-br from-[#0a0d12] via-[#0d1018] to-[#0a0d12] opacity-80"></div>
    <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent"></div>
    
    <!-- Content Sections -->
    <div class="relative z-10">
      <HeroSection />
      <ValuePropositions />
      <TestimonialsSection />
      <HiringPositionsSection />
      <ServicePackagesSection />
      <BlogsSection />
      <AboutSection />
      <ContactSection />
      <PageFooter />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useMetadata } from '~/utils/seo';

// Components
import HeroSection from '~/components/home/<USER>';
import ValuePropositions from '~/components/home/<USER>';
import TestimonialsSection from '~/components/home/<USER>';
import HiringPositionsSection from '~/components/home/<USER>';
import ServicePackagesSection from '~/components/home/<USER>';
import BlogsSection from '~/components/home/<USER>';
import AboutSection from '~/components/home/<USER>';
import ContactSection from '~/components/home/<USER>';
import PageFooter from '~/components/layout/PageFooter.vue';

// Set page metadata
const { setPageMetadata } = useMetadata();
onMounted(() => {
  setPageMetadata({
    title: 'Home',
    description: 'Welcome to Al-Alwi - Creative developer and designer building beautiful web experiences'
  });
});
</script>

<style scoped>
/* Enhanced Home Page Styling */
.home-page {
  scroll-behavior: smooth;
  position: relative;
}

/* Improved section animations with better timing */
.home-page .relative.z-10 > * {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(60px);
}

.home-page .relative.z-10 > *:nth-child(1) { animation-delay: 0.1s; }
.home-page .relative.z-10 > *:nth-child(2) { animation-delay: 0.2s; }
.home-page .relative.z-10 > *:nth-child(3) { animation-delay: 0.3s; }
.home-page .relative.z-10 > *:nth-child(4) { animation-delay: 0.4s; }
.home-page .relative.z-10 > *:nth-child(5) { animation-delay: 0.5s; }
.home-page .relative.z-10 > *:nth-child(6) { animation-delay: 0.6s; }
.home-page .relative.z-10 > *:nth-child(7) { animation-delay: 0.7s; }
.home-page .relative.z-10 > *:nth-child(8) { animation-delay: 0.8s; }
.home-page .relative.z-10 > *:nth-child(9) { animation-delay: 0.9s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design enhancements */
@media (max-width: 1440px) {
  .home-page {
    padding: 0 3rem;
  }
}

@media (max-width: 1200px) {
  .home-page {
    padding: 0 2rem;
  }
}

@media (max-width: 768px) {
  .home-page {
    padding: 0;
  }
  
  /* Faster animations on mobile for better performance */
  .home-page .relative.z-10 > * {
    animation-duration: 0.6s;
    transform: translateY(30px);
  }
}

@media (max-width: 480px) {
  .home-page {
    padding: 0;
  }
  
  /* Even faster animations for small screens */
  .home-page .relative.z-10 > * {
    animation-duration: 0.4s;
    transform: translateY(20px);
  }
}

/* Smooth scrolling for all browsers */
* {
  scroll-behavior: smooth;
}

/* Background gradient animation */
.home-page::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(21, 111, 238, 0.05) 0%,
    transparent 50%,
    rgba(21, 111, 238, 0.05) 100%
  );
  animation: rotateBackground 20s linear infinite;
  z-index: 1;
}

@keyframes rotateBackground {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Performance optimizations */
.home-page {
  will-change: scroll-position;
  transform: translateZ(0);
}

.home-page .relative.z-10 > * {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* Improve text rendering */
.home-page {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Custom scrollbar styling */
.home-page::-webkit-scrollbar {
  width: 8px;
}

.home-page::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.home-page::-webkit-scrollbar-thumb {
  background: rgba(21, 111, 238, 0.6);
  border-radius: 4px;
}

.home-page::-webkit-scrollbar-thumb:hover {
  background: rgba(21, 111, 238, 0.8);
}
</style>